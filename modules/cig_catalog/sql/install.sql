-- CIG Catalog Module Installation SQL
-- Creates database tables for catalog management system

-- Main catalog table
CREATE TABLE IF NOT EXISTS `PREFIX_cig_catalog` (
    `id_catalog` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `active` tinyint(1) unsigned NOT NULL DEFAULT '1',
    `position` int(10) unsigned NOT NULL DEFAULT '0',
    `is_new` tinyint(1) unsigned NOT NULL DEFAULT '0',
    `image` varchar(255) DEFAULT NULL,
    `catalog_file` varchar(255) DEFAULT NULL,
    `catalog_url` varchar(500) DEFAULT NULL,
    `download_count` int(10) unsigned NOT NULL DEFAULT '0',
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_catalog`),
    KEY `active` (`active`),
    KEY `position` (`position`),
    KEY `is_new` (`is_new`),
    KEY `date_add` (`date_add`)
) ENGINE=ENGINE_TYPE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Multilingual catalog data
CREATE TABLE IF NOT EXISTS `PREFIX_cig_catalog_lang` (
    `id_catalog` int(10) unsigned NOT NULL,
    `id_lang` int(10) unsigned NOT NULL,
    `name` varchar(255) NOT NULL,
    `description` text,
    `short_description` varchar(500) DEFAULT NULL,
    `meta_title` varchar(255) DEFAULT NULL,
    `meta_description` varchar(500) DEFAULT NULL,
    `slug` varchar(255) DEFAULT NULL,
    PRIMARY KEY (`id_catalog`, `id_lang`),
    KEY `id_catalog` (`id_catalog`),
    KEY `id_lang` (`id_lang`),
    KEY `slug` (`slug`),
    KEY `name` (`name`),
    CONSTRAINT `FK_cig_catalog_lang_catalog` 
        FOREIGN KEY (`id_catalog`) 
        REFERENCES `PREFIX_cig_catalog` (`id_catalog`) 
        ON DELETE CASCADE
) ENGINE=ENGINE_TYPE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Catalog orders table
CREATE TABLE IF NOT EXISTS `PREFIX_cig_catalog_order` (
    `id_order` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `id_catalog` int(10) unsigned NOT NULL,
    `customer_name` varchar(255) NOT NULL,
    `customer_email` varchar(255) NOT NULL,
    `customer_phone` varchar(50) DEFAULT NULL,
    `company_name` varchar(255) DEFAULT NULL,
    `company_ico` varchar(20) DEFAULT NULL,
    `address` varchar(500) DEFAULT NULL,
    `city` varchar(100) DEFAULT NULL,
    `postal_code` varchar(20) DEFAULT NULL,
    `country` varchar(100) DEFAULT NULL,
    `note` text,
    `ip_address` varchar(45) DEFAULT NULL,
    `user_agent` varchar(500) DEFAULT NULL,
    `status` enum('pending', 'processed', 'sent', 'cancelled') NOT NULL DEFAULT 'pending',
    `admin_note` text,
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_order`),
    KEY `id_catalog` (`id_catalog`),
    KEY `customer_email` (`customer_email`),
    KEY `status` (`status`),
    KEY `date_add` (`date_add`),
    KEY `ip_address` (`ip_address`),
    CONSTRAINT `FK_cig_catalog_order_catalog` 
        FOREIGN KEY (`id_catalog`) 
        REFERENCES `PREFIX_cig_catalog` (`id_catalog`) 
        ON DELETE CASCADE
) ENGINE=ENGINE_TYPE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Configuration table
CREATE TABLE IF NOT EXISTS `PREFIX_cig_catalog_config` (
    `id_config` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `config_key` varchar(100) NOT NULL,
    `config_value` text,
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_config`),
    UNIQUE KEY `config_key` (`config_key`)
) ENGINE=ENGINE_TYPE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default configuration values
INSERT INTO `PREFIX_cig_catalog_config` (`config_key`, `config_value`, `date_add`, `date_upd`) VALUES
('items_per_page', '12', NOW(), NOW()),
('enable_ordering', '1', NOW(), NOW()),
('admin_email', '', NOW(), NOW()),
('from_name', 'CIG Catalog System', NOW(), NOW()),
('from_email', '', NOW(), NOW()),
('smtp_enabled', '0', NOW(), NOW()),
('smtp_host', '', NOW(), NOW()),
('smtp_port', '587', NOW(), NOW()),
('smtp_username', '', NOW(), NOW()),
('smtp_password', '', NOW(), NOW()),
('smtp_encryption', 'tls', NOW(), NOW()),
('max_file_size', '10485760', NOW(), NOW()),
('allowed_image_types', 'jpg,jpeg,png,gif,webp', NOW(), NOW()),
('allowed_file_types', 'pdf,zip,doc,docx,xls,xlsx', NOW(), NOW()),
('enable_download_tracking', '1', NOW(), NOW()),
('enable_new_badge', '1', NOW(), NOW()),
('new_badge_days', '30', NOW(), NOW()),
('enable_pagination', '1', NOW(), NOW()),
('enable_search', '1', NOW(), NOW()),
('cache_enabled', '1', NOW(), NOW()),
('cache_ttl', '3600', NOW(), NOW());

-- Create indexes for better performance
CREATE INDEX `idx_catalog_active_position` ON `PREFIX_cig_catalog` (`active`, `position`);
CREATE INDEX `idx_catalog_lang_name` ON `PREFIX_cig_catalog_lang` (`name`);
CREATE INDEX `idx_catalog_order_status_date` ON `PREFIX_cig_catalog_order` (`status`, `date_add`);

-- Create full-text search index for catalog names and descriptions
ALTER TABLE `PREFIX_cig_catalog_lang` ADD FULLTEXT(`name`, `description`, `short_description`);
